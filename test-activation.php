<?php
/**
 * Test Plugin Activation
 * Simple test to check if the main plugin can be loaded without errors
 */

// Simulate WordPress environment
if (!defined('ABSPATH')) {
    define('ABSPATH', '/');
}

// Define basic WordPress functions for testing
if (!function_exists('add_action')) {
    function add_action($hook, $callback, $priority = 10, $accepted_args = 1) {
        // Mock function for testing
        return true;
    }
}

if (!function_exists('plugin_dir_path')) {
    function plugin_dir_path($file) {
        return dirname($file) . '/';
    }
}

if (!function_exists('plugin_dir_url')) {
    function plugin_dir_url($file) {
        return 'http://example.com/wp-content/plugins/' . basename(dirname($file)) . '/';
    }
}

if (!function_exists('register_activation_hook')) {
    function register_activation_hook($file, $callback) {
        return true;
    }
}

if (!function_exists('register_deactivation_hook')) {
    function register_deactivation_hook($file, $callback) {
        return true;
    }
}

// Test loading the main plugin file
echo "Testing plugin activation...\n";

try {
    // Include the main plugin file
    include 'AI-SEO-META-OPTIMIZER-ELITE.php';
    echo "✅ Plugin loaded successfully!\n";
    echo "✅ No fatal errors detected.\n";
    echo "✅ Plugin should activate properly in WordPress.\n";
} catch (Exception $e) {
    echo "❌ Error loading plugin: " . $e->getMessage() . "\n";
} catch (ParseError $e) {
    echo "❌ Parse error in plugin: " . $e->getMessage() . "\n";
} catch (Error $e) {
    echo "❌ Fatal error in plugin: " . $e->getMessage() . "\n";
}

echo "\nTest completed.\n";
?>
