<?php
// Simple syntax check for the plugin file

echo "Checking PHP syntax...\n";

$file = 'AI-SEO-META-OPTIMIZER-ELITE.php';

if (!file_exists($file)) {
    echo "❌ File not found: $file\n";
    exit(1);
}

// Read the file content
$content = file_get_contents($file);

if ($content === false) {
    echo "❌ Could not read file: $file\n";
    exit(1);
}

// Check for basic syntax issues
$issues = [];

// Check for unmatched braces
$open_braces = substr_count($content, '{');
$close_braces = substr_count($content, '}');

if ($open_braces !== $close_braces) {
    $issues[] = "Unmatched braces: $open_braces open, $close_braces close";
}

// Check for unmatched parentheses
$open_parens = substr_count($content, '(');
$close_parens = substr_count($content, ')');

if ($open_parens !== $close_parens) {
    $issues[] = "Unmatched parentheses: $open_parens open, $close_parens close";
}

// Check for PHP opening tag
if (strpos($content, '<?php') !== 0) {
    $issues[] = "File does not start with <?php";
}

// Check for class definition
if (strpos($content, 'class AI_SEO_Meta_Elite_Fixed') === false) {
    $issues[] = "Main class not found";
}

// Check for plugin header
if (strpos($content, 'Plugin Name:') === false) {
    $issues[] = "Plugin header not found";
}

if (empty($issues)) {
    echo "✅ Basic syntax checks passed!\n";
    echo "✅ File structure looks correct.\n";
    echo "✅ Plugin should be ready for activation.\n";
} else {
    echo "❌ Issues found:\n";
    foreach ($issues as $issue) {
        echo "  - $issue\n";
    }
}

echo "\nFile size: " . number_format(strlen($content)) . " bytes\n";
echo "Lines: " . substr_count($content, "\n") . "\n";

echo "\nSyntax check completed.\n";
?>
