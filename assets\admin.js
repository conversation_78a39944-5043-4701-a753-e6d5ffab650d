/**
 * AI SEO Meta Optimizer - Elite Edition - Admin JavaScript
 * COMPLETELY REWRITTEN FOR MAXIMUM EFFICIENCY AND FUNCTIONALITY
 */

jQuery(document).ready(function($) {
    'use strict';
    
    // Global variables
    let currentPage = 1;
    let isOptimizing = false;
    let bulkQueue = [];
    let bulkProgress = 0;
    
    /**
     * Initialize the admin interface
     */
    function initializeAdmin() {
        loadDashboard();
        bindEvents();
        setupModals();
    }
    
    /**
     * Load dashboard data
     */
    function loadDashboard() {
        if ($('#dashboard-content').length) {
            showLoading('#dashboard-content');
            
            $.ajax({
                url: aiSeoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'ai_seo_get_dashboard_data',
                    nonce: aiSeoAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        renderDashboard(response.data);
                    } else {
                        showError('#dashboard-content', response.data);
                    }
                },
                error: function() {
                    showError('#dashboard-content', 'Failed to load dashboard data');
                }
            });
        }
    }
    
    /**
     * Load meta manager
     */
    function loadMetaManager(page = 1, search = '', postType = 'post') {
        if ($('#meta-manager-content').length) {
            showLoading('#meta-manager-content');
            
            $.ajax({
                url: aiSeoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'ai_seo_get_posts',
                    nonce: aiSeoAjax.nonce,
                    page: page,
                    per_page: 20,
                    search: search,
                    post_type: postType
                },
                success: function(response) {
                    if (response.success) {
                        renderMetaManager(response.data);
                        currentPage = page;
                    } else {
                        showError('#meta-manager-content', response.data);
                    }
                },
                error: function() {
                    showError('#meta-manager-content', 'Failed to load posts');
                }
            });
        }
    }
    
    /**
     * Optimize single post
     */
    function optimizePost(postId, button) {
        if (isOptimizing) return;
        
        isOptimizing = true;
        const originalText = button.html();
        button.html('<span class="spinner is-active"></span> Optimizing...').prop('disabled', true);
        
        $.ajax({
            url: aiSeoAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'ai_seo_optimize_meta',
                nonce: aiSeoAjax.nonce,
                post_id: postId
            },
            success: function(response) {
                if (response.success) {
                    showOptimizationResult(response.data);
                    updatePostRow(postId, response.data);
                    showNotification('Post optimized successfully!', 'success');
                } else {
                    showNotification('Optimization failed: ' + response.data, 'error');
                }
            },
            error: function() {
                showNotification('Network error occurred', 'error');
            },
            complete: function() {
                isOptimizing = false;
                button.html(originalText).prop('disabled', false);
            }
        });
    }
    
    /**
     * Update meta data manually
     */
    function updateMetaData(postId, title, description) {
        $.ajax({
            url: aiSeoAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'ai_seo_update_meta',
                nonce: aiSeoAjax.nonce,
                post_id: postId,
                meta_title: title,
                meta_description: description
            },
            success: function(response) {
                if (response.success) {
                    showNotification('Meta data updated successfully!', 'success');
                    closeModal();
                    loadMetaManager(currentPage);
                } else {
                    showNotification('Update failed: ' + response.data, 'error');
                }
            },
            error: function() {
                showNotification('Network error occurred', 'error');
            }
        });
    }
    
    /**
     * Test API connection
     */
    function testApiConnection(provider, apiKey) {
        const button = $('#test-api-' + provider);
        const originalText = button.html();
        button.html('<span class="spinner is-active"></span> Testing...').prop('disabled', true);
        
        $.ajax({
            url: aiSeoAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'ai_seo_test_api',
                nonce: aiSeoAjax.nonce,
                provider: provider,
                api_key: apiKey
            },
            success: function(response) {
                if (response.success) {
                    showNotification('API connection successful!', 'success');
                    button.addClass('button-primary').removeClass('button-secondary');
                } else {
                    showNotification('API test failed: ' + response.data, 'error');
                    button.addClass('button-secondary').removeClass('button-primary');
                }
            },
            error: function() {
                showNotification('Network error occurred', 'error');
            },
            complete: function() {
                button.html(originalText).prop('disabled', false);
            }
        });
    }
    
    /**
     * Save settings
     */
    function saveSettings() {
        const formData = $('#ai-seo-settings-form').serialize();
        const button = $('#save-settings');
        const originalText = button.html();
        
        button.html('<span class="spinner is-active"></span> Saving...').prop('disabled', true);
        
        $.ajax({
            url: aiSeoAjax.ajaxurl,
            type: 'POST',
            data: formData + '&action=ai_seo_save_settings&nonce=' + aiSeoAjax.nonce,
            success: function(response) {
                if (response.success) {
                    showNotification('Settings saved successfully!', 'success');
                } else {
                    showNotification('Save failed: ' + response.data, 'error');
                }
            },
            error: function() {
                showNotification('Network error occurred', 'error');
            },
            complete: function() {
                button.html(originalText).prop('disabled', false);
            }
        });
    }
    
    /**
     * Bind event handlers
     */
    function bindEvents() {
        // Meta manager events
        $(document).on('click', '.optimize-post-btn', function(e) {
            e.preventDefault();
            const postId = $(this).data('post-id');
            if (confirm(aiSeoAjax.strings.confirm_optimize)) {
                optimizePost(postId, $(this));
            }
        });
        
        $(document).on('click', '.edit-meta-btn', function(e) {
            e.preventDefault();
            const postId = $(this).data('post-id');
            openEditModal(postId);
        });
        
        // Settings events
        $(document).on('click', '[id^="test-api-"]', function(e) {
            e.preventDefault();
            const provider = $(this).attr('id').replace('test-api-', '');
            const apiKey = $('#' + provider + '_api_key').val();
            if (apiKey) {
                testApiConnection(provider, apiKey);
            } else {
                showNotification('Please enter API key first', 'error');
            }
        });
        
        $(document).on('click', '#save-settings', function(e) {
            e.preventDefault();
            saveSettings();
        });
        
        // Modal events
        $(document).on('click', '.modal-close, .modal-overlay', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
        
        $(document).on('click', '#save-meta-changes', function(e) {
            e.preventDefault();
            const postId = $(this).data('post-id');
            const title = $('#edit-meta-title').val();
            const description = $('#edit-meta-description').val();
            updateMetaData(postId, title, description);
        });
        
        // Character counters
        $(document).on('input', '#edit-meta-title', function() {
            updateCharCounter(this, 60);
        });
        
        $(document).on('input', '#edit-meta-description', function() {
            updateCharCounter(this, 160);
        });
        
        // Pagination
        $(document).on('click', '.pagination-btn', function(e) {
            e.preventDefault();
            const page = $(this).data('page');
            loadMetaManager(page);
        });
        
        // Search
        $(document).on('submit', '#meta-search-form', function(e) {
            e.preventDefault();
            const search = $('#meta-search').val();
            const postType = $('#post-type-filter').val();
            loadMetaManager(1, search, postType);
        });
        
        // Post editor meta box
        $(document).on('click', '#ai-optimize-post', function(e) {
            e.preventDefault();
            const postId = $(this).data('post-id');
            optimizePost(postId, $(this));
        });
        
        $(document).on('click', '#analyze-seo', function(e) {
            e.preventDefault();
            const postId = $(this).data('post-id');
            analyzeSeoScore(postId);
        });
    }
    
    /**
     * Setup modals
     */
    function setupModals() {
        if ($('#ai-seo-modal').length === 0) {
            $('body').append(`
                <div id="ai-seo-modal" class="ai-seo-modal" style="display: none;">
                    <div class="modal-overlay"></div>
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 id="modal-title">Modal Title</h3>
                            <button class="modal-close">&times;</button>
                        </div>
                        <div class="modal-body" id="modal-body">
                            Modal content
                        </div>
                        <div class="modal-footer" id="modal-footer">
                            <!-- Footer buttons will be added dynamically -->
                        </div>
                    </div>
                </div>
            `);
        }
    }
    
    /**
     * Utility functions
     */
    function showLoading(selector) {
        $(selector).html('<div class="loading-spinner"><span class="spinner is-active"></span> Loading...</div>');
    }
    
    function showError(selector, message) {
        $(selector).html('<div class="error-message">❌ ' + message + '</div>');
    }
    
    function showNotification(message, type = 'info') {
        const notification = $(`
            <div class="ai-seo-notification notification-${type}">
                <span class="notification-icon">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
                <span class="notification-message">${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `);
        
        $('body').append(notification);
        
        setTimeout(() => {
            notification.fadeOut(() => notification.remove());
        }, 5000);
        
        notification.find('.notification-close').on('click', () => {
            notification.fadeOut(() => notification.remove());
        });
    }
    
    function updateCharCounter(input, maxLength) {
        const length = $(input).val().length;
        const counter = $(input).siblings('.char-counter');
        
        if (counter.length === 0) {
            $(input).after(`<div class="char-counter">${length}/${maxLength}</div>`);
        } else {
            counter.text(`${length}/${maxLength}`);
        }
        
        if (length > maxLength) {
            counter.addClass('over-limit');
        } else {
            counter.removeClass('over-limit');
        }
    }
    
    function openModal(title, content, footer = '') {
        $('#modal-title').text(title);
        $('#modal-body').html(content);
        $('#modal-footer').html(footer);
        $('#ai-seo-modal').fadeIn();
    }
    
    function closeModal() {
        $('#ai-seo-modal').fadeOut();
    }
    
    // Initialize when document is ready
    initializeAdmin();
    
    /**
     * Render dashboard
     */
    function renderDashboard(data) {
        const content = `
            <div class="dashboard-stats">
                <div class="stat-card">
                    <span class="stat-number">${data.total_posts}</span>
                    <span class="stat-label">Total Posts</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">${data.optimized_posts}</span>
                    <span class="stat-label">Optimized Posts</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">${data.optimization_percentage}%</span>
                    <span class="stat-label">Optimization Rate</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">+${data.avg_improvement}</span>
                    <span class="stat-label">Avg Score Improvement</span>
                </div>
            </div>

            <div class="dashboard-actions">
                <h3>Quick Actions</h3>
                <div class="action-buttons">
                    <a href="admin.php?page=ai-seo-meta-manager" class="btn btn-primary">📊 Manage All Meta Data</a>
                    <a href="admin.php?page=ai-seo-bulk-optimizer" class="btn btn-success">⚡ Bulk Optimize</a>
                    <a href="admin.php?page=ai-seo-settings" class="btn btn-secondary">⚙️ Settings</a>
                </div>
            </div>

            <div class="recent-optimizations">
                <h3>Recent Optimizations</h3>
                <div class="optimization-list">
                    ${data.recent_optimizations.map(opt => `
                        <div class="optimization-item">
                            <strong>${opt.post_title}</strong>
                            <span class="improvement">+${opt.new_score - opt.old_score} points</span>
                            <span class="date">${new Date(opt.optimization_date).toLocaleDateString()}</span>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;

        $('#dashboard-content').html(content);
    }

    /**
     * Render meta manager
     */
    function renderMetaManager(data) {
        const content = `
            <div class="meta-manager-header">
                <h2>📊 All Posts Meta Data Overview</h2>
                <div class="meta-manager-controls">
                    <div class="search-box">
                        <form id="meta-search-form">
                            <input type="text" id="meta-search" placeholder="Search posts..." value="">
                            <select id="post-type-filter">
                                <option value="post">Posts</option>
                                <option value="page">Pages</option>
                            </select>
                            <button type="submit" class="btn btn-primary">Search</button>
                        </form>
                    </div>
                    <div class="bulk-actions">
                        <button class="btn btn-success" id="bulk-optimize-selected">⚡ Optimize Selected</button>
                    </div>
                </div>
            </div>

            <div class="posts-table-container">
                <table class="posts-table">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="select-all"></th>
                            <th>Post Title</th>
                            <th>URL</th>
                            <th>Meta Title</th>
                            <th>Meta Description</th>
                            <th>SEO Score</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.posts.map(post => `
                            <tr data-post-id="${post.id}">
                                <td><input type="checkbox" class="post-checkbox" value="${post.id}"></td>
                                <td><strong>${post.title}</strong></td>
                                <td><a href="${post.url}" target="_blank">🔗 View</a></td>
                                <td class="meta-preview">${post.meta_title}</td>
                                <td class="meta-preview">${post.meta_description}</td>
                                <td><span class="seo-score ${post.seo_score >= 80 ? 'excellent' : post.seo_score >= 60 ? 'good' : 'poor'}">${post.seo_score}/100</span></td>
                                <td>${post.date}</td>
                                <td class="action-buttons">
                                    <button class="btn btn-primary optimize-post-btn" data-post-id="${post.id}">🤖 Optimize</button>
                                    <button class="btn btn-secondary edit-meta-btn" data-post-id="${post.id}">✏️ Edit</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>

            <div class="pagination">
                ${renderPagination(data.current_page, data.total_pages)}
            </div>
        `;

        $('#meta-manager-content').html(content);
    }

    /**
     * Render pagination
     */
    function renderPagination(currentPage, totalPages) {
        let pagination = '';

        if (currentPage > 1) {
            pagination += `<a href="#" class="pagination-btn" data-page="${currentPage - 1}">« Previous</a>`;
        }

        for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
            pagination += `<a href="#" class="pagination-btn ${i === currentPage ? 'current' : ''}" data-page="${i}">${i}</a>`;
        }

        if (currentPage < totalPages) {
            pagination += `<a href="#" class="pagination-btn" data-page="${currentPage + 1}">Next »</a>`;
        }

        return pagination;
    }

    /**
     * Open edit modal
     */
    function openEditModal(postId) {
        const row = $(`tr[data-post-id="${postId}"]`);
        const postTitle = row.find('td:nth-child(2) strong').text();
        const metaTitle = row.find('.meta-preview:first').text();
        const metaDescription = row.find('.meta-preview:last').text();

        const modalContent = `
            <div class="form-group">
                <label>Post Title:</label>
                <input type="text" value="${postTitle}" readonly>
            </div>
            <div class="form-group">
                <label for="edit-meta-title">Meta Title:</label>
                <input type="text" id="edit-meta-title" value="${metaTitle}" maxlength="60">
                <div class="char-counter">0/60</div>
            </div>
            <div class="form-group">
                <label for="edit-meta-description">Meta Description:</label>
                <textarea id="edit-meta-description" rows="3" maxlength="160">${metaDescription}</textarea>
                <div class="char-counter">0/160</div>
            </div>
        `;

        const modalFooter = `
            <button class="btn btn-secondary" onclick="closeModal()">Cancel</button>
            <button class="btn btn-primary" id="save-meta-changes" data-post-id="${postId}">💾 Save Changes</button>
            <button class="btn btn-success" id="ai-optimize-modal" data-post-id="${postId}">🤖 AI Optimize</button>
        `;

        openModal('✏️ Edit Meta Data', modalContent, modalFooter);

        // Update character counters
        updateCharCounter($('#edit-meta-title')[0], 60);
        updateCharCounter($('#edit-meta-description')[0], 160);
    }

    /**
     * Show optimization result
     */
    function showOptimizationResult(data) {
        const modalContent = `
            <div class="optimization-result">
                <div class="score-comparison">
                    <div class="score-item">
                        <span class="label">Old Score:</span>
                        <span class="score old-score">${data.old_score}/100</span>
                    </div>
                    <div class="score-item">
                        <span class="label">New Score:</span>
                        <span class="score new-score">${data.new_score}/100</span>
                    </div>
                    <div class="score-item">
                        <span class="label">Improvement:</span>
                        <span class="score improvement">+${data.improvement} points</span>
                    </div>
                </div>

                <div class="meta-comparison">
                    <div class="meta-item">
                        <h4>Optimized Title:</h4>
                        <div class="meta-preview new">${data.optimized_title}</div>
                    </div>
                    <div class="meta-item">
                        <h4>Optimized Description:</h4>
                        <div class="meta-preview new">${data.optimized_description}</div>
                    </div>
                </div>
            </div>
        `;

        openModal('🎉 Optimization Complete!', modalContent, '<button class="btn btn-primary" onclick="closeModal()">Close</button>');
    }

    /**
     * Update post row after optimization
     */
    function updatePostRow(postId, data) {
        const row = $(`tr[data-post-id="${postId}"]`);
        row.find('.meta-preview:first').text(data.optimized_title);
        row.find('.meta-preview:last').text(data.optimized_description);
        row.find('.seo-score').text(data.new_score + '/100')
            .removeClass('excellent good poor')
            .addClass(data.new_score >= 80 ? 'excellent' : data.new_score >= 60 ? 'good' : 'poor');
    }

    /**
     * Analyze SEO score
     */
    function analyzeSeoScore(postId) {
        $.ajax({
            url: aiSeoAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'ai_seo_analyze_seo',
                nonce: aiSeoAjax.nonce,
                post_id: postId
            },
            success: function(response) {
                if (response.success) {
                    showSeoAnalysis(response.data);
                } else {
                    showNotification('Analysis failed: ' + response.data, 'error');
                }
            },
            error: function() {
                showNotification('Network error occurred', 'error');
            }
        });
    }

    /**
     * Show SEO analysis
     */
    function showSeoAnalysis(data) {
        const modalContent = `
            <div class="seo-analysis">
                <div class="current-score">
                    <h3>Current SEO Score: <span class="score-badge ${data.seo_score >= 80 ? 'excellent' : data.seo_score >= 60 ? 'good' : 'poor'}">${data.seo_score}/100</span></h3>
                </div>

                <div class="analysis-sections">
                    <div class="analysis-section">
                        <h4>📝 Title Analysis</h4>
                        <ul>
                            ${data.analysis.title_analysis.map(item => `<li>${item}</li>`).join('')}
                        </ul>
                    </div>

                    <div class="analysis-section">
                        <h4>📄 Description Analysis</h4>
                        <ul>
                            ${data.analysis.description_analysis.map(item => `<li>${item}</li>`).join('')}
                        </ul>
                    </div>

                    <div class="analysis-section">
                        <h4>💡 Recommendations</h4>
                        <ul>
                            ${data.analysis.recommendations.map(item => `<li>${item}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            </div>
        `;

        openModal('🔍 SEO Analysis Results', modalContent, '<button class="btn btn-primary" onclick="closeModal()">Close</button>');
    }

    // Load appropriate page content based on current page
    const currentPage = window.location.href;

    if (currentPage.indexOf('ai-seo-meta-manager') !== -1) {
        loadMetaManager();
    } else if (currentPage.indexOf('ai-seo-bulk-optimizer') !== -1) {
        // Bulk optimizer content is already rendered in PHP
        console.log('Bulk optimizer page loaded');
    } else if (currentPage.indexOf('ai-seo-settings') !== -1) {
        // Settings content is already rendered in PHP
        console.log('Settings page loaded');
    } else if (currentPage.indexOf('ai-seo-meta-elite') !== -1 && currentPage.indexOf('page=ai-seo-meta-elite') !== -1) {
        // Dashboard page
        loadDashboard();
    }
});
