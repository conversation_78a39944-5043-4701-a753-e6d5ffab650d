/**
 * AI SEO Meta Optimizer - Elite Edition - Admin Styles
 * ULTRA-MODERN, RESPONSIVE, PROFESSIONAL DESIGN
 */

/* Global Styles */
.ai-seo-admin {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Dashboard Styles */
.ai-seo-dashboard {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 30px;
    color: white;
    margin: 20px 0;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.stat-card {
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-size: 2.5em;
    font-weight: bold;
    margin-bottom: 10px;
    display: block;
}

.stat-label {
    font-size: 1.1em;
    opacity: 0.9;
}

/* Meta Manager Styles */
.ai-seo-meta-manager {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.meta-manager-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
}

.meta-manager-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.search-box {
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-box input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    min-width: 200px;
}

.posts-table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
}

.posts-table th,
.posts-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.posts-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.posts-table tr:hover {
    background: #f8f9fa;
}

.seo-score {
    padding: 4px 8px;
    border-radius: 12px;
    color: white;
    font-weight: bold;
    font-size: 0.9em;
}

.seo-score.excellent { background: #4CAF50; }
.seo-score.good { background: #FF9800; }
.seo-score.poor { background: #F44336; }

.meta-preview {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.action-buttons {
    display: flex;
    gap: 8px;
}

.btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9em;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #007cba;
    color: white;
}

.btn-primary:hover {
    background: #005a87;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #1e7e34;
}

/* Modal Styles */
.ai-seo-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    z-index: 1;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px 8px 0 0;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.3em;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5em;
    cursor: pointer;
    color: white;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.modal-close:hover {
    background: rgba(255,255,255,0.2);
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0,124,186,0.2);
}

.char-counter {
    font-size: 0.9em;
    color: #666;
    margin-top: 5px;
}

.char-counter.over-limit {
    color: #F44336;
    font-weight: bold;
}

/* Notification Styles */
.ai-seo-notification {
    position: fixed;
    top: 32px;
    right: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.2);
    padding: 15px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    z-index: 100001;
    min-width: 300px;
    border-left: 4px solid #007cba;
}

.notification-success {
    border-left-color: #4CAF50;
}

.notification-error {
    border-left-color: #F44336;
}

.notification-icon {
    font-size: 1.2em;
}

.notification-message {
    flex: 1;
    font-weight: 500;
}

.notification-close {
    background: none;
    border: none;
    font-size: 1.2em;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Loading Styles */
.loading-spinner {
    text-align: center;
    padding: 40px;
    color: #666;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.spinner.is-active {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error Styles */
.error-message {
    text-align: center;
    padding: 40px;
    color: #F44336;
    font-weight: 500;
}

/* Pagination Styles */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin: 20px 0;
}

.pagination-btn {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background: white;
    color: #333;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.pagination-btn:hover {
    background: #f8f9fa;
    border-color: #007cba;
}

.pagination-btn.current {
    background: #007cba;
    color: white;
    border-color: #007cba;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-stats {
        grid-template-columns: 1fr;
    }
    
    .meta-manager-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-box {
        flex-direction: column;
    }
    
    .search-box input {
        min-width: auto;
    }
    
    .posts-table {
        font-size: 0.9em;
    }
    
    .posts-table th,
    .posts-table td {
        padding: 8px;
    }
    
    .modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}

/* Settings Page Styles */
.ai-seo-settings {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.settings-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
}

.settings-content {
    padding: 30px;
}

.settings-section {
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 1px solid #eee;
}

.settings-section:last-child {
    border-bottom: none;
}

.settings-section h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #333;
    font-size: 1.3em;
}

.api-provider-card {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    transition: border-color 0.3s ease;
}

.api-provider-card.active {
    border-color: #007cba;
    background: #f8f9fa;
}

.provider-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.provider-name {
    font-weight: 600;
    font-size: 1.1em;
}

.provider-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
}

.provider-status.connected {
    background: #4CAF50;
    color: white;
}

.provider-status.disconnected {
    background: #F44336;
    color: white;
}

.api-key-input {
    display: flex;
    gap: 10px;
    align-items: center;
}

.api-key-input input {
    flex: 1;
}

/* Bulk Optimizer Styles */
.ai-seo-bulk-optimizer {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.bulk-optimizer-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
}

.bulk-controls {
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #eee;
}

.bulk-progress {
    padding: 20px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #eee;
    border-radius: 10px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: width 0.3s ease;
    border-radius: 10px;
}

.progress-text {
    text-align: center;
    margin-top: 10px;
    font-weight: 500;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(100%); }
    to { transform: translateY(0); }
}
